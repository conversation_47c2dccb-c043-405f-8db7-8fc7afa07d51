import { useFormContext, useFieldArray } from 'react-hook-form';
import { IProduct } from '@/apis/product/product.type';
import { installationType } from '@/constants/sharedData/sharedData';
import { formatUSD } from '@/utils/format';
import { Button, Col, Table } from 'reactstrap';
import { useEffect, useState } from 'react';

interface TableProductProps {
    initValue?: IProduct;
    page: string;
}

type ProductFieldPath =
    | `productOptions.${number}.basePrice`
    | `productOptions.${number}.userCount`;

interface CurrencyInputProps {
    name: ProductFieldPath | string;
    placeholder: string;
    value?: number;
    setValue: ReturnType<typeof useFormContext<IProduct>>['setValue'];
}

const CurrencyInput = ({
    name,
    placeholder,
    value,
    setValue,
}: CurrencyInputProps) => {
    const [isFocused, setIsFocused] = useState(false);
    const [displayValue, setDisplayValue] = useState('');

    useEffect(() => {
        if (!isFocused) {
            // Chỉ hiển thị giá trị khi nó thực sự hợp lệ và lớn hơn 0
            if (
                value !== undefined &&
                value !== null &&
                !isNaN(value) &&
                value > 0
            ) {
                try {
                    const formattedValue = formatUSD(value);
                    // Kiểm tra thêm để đảm bảo không hiển thị NaN
                    if (
                        formattedValue &&
                        formattedValue !== 'NaN' &&
                        formattedValue !== '0'
                    ) {
                        setDisplayValue(formattedValue);
                    } else {
                        setDisplayValue('');
                    }
                } catch {
                    setDisplayValue('');
                }
            } else {
                setDisplayValue('');
            }
        }
    }, [value, isFocused]);

    const handleFocus = () => {
        setIsFocused(true);
        // Đảm bảo displayValue trống
        setDisplayValue('');
        // Clear giá trị trong form để tránh NaN
        setValue(name as ProductFieldPath, undefined);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        // Xử lý cả dấu phẩy và dấu chấm
        const inputValue = e.target.value.replace(/,/g, '.');

        // Nếu input trống hoặc chỉ có dấu chấm/dấu phẩy, để trống
        if (inputValue === '' || inputValue === '.' || inputValue === ',') {
            setValue(name as ProductFieldPath, undefined);
            setDisplayValue('');
            return;
        }

        const numValue = parseFloat(inputValue);

        // Cập nhật giá trị trong form (chỉ khi giá trị hợp lệ)
        if (!isNaN(numValue) && numValue > 0) {
            setValue(name as ProductFieldPath, numValue);
            try {
                const formattedValue = formatUSD(numValue);
                // Kiểm tra thêm để đảm bảo không hiển thị NaN
                if (
                    formattedValue &&
                    formattedValue !== 'NaN' &&
                    formattedValue !== '0'
                ) {
                    setDisplayValue(formattedValue);
                } else {
                    setDisplayValue('');
                }
            } catch {
                setDisplayValue('');
            }
        } else {
            setValue(name as ProductFieldPath, undefined);
            setDisplayValue('');
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        // Cho phép nhập cả dấu phẩy và dấu chấm
        const value = e.target.value;
        const regex = /^[0-9]*[.,]?[0-9]*$/;

        if (value === '' || regex.test(value)) {
            e.target.value = value;
        }
    };

    return (
        <>
            {isFocused ? (
                <input
                    type='text'
                    placeholder={placeholder}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    onChange={handleInputChange}
                    defaultValue=''
                    autoFocus
                    style={{
                        padding: '4px 0px 4px 0px',
                        width: '100%',
                        border: 'none',
                        background: 'transparent',
                    }}
                />
            ) : (
                <input
                    type='text'
                    value={displayValue}
                    placeholder={placeholder}
                    onFocus={handleFocus}
                    readOnly
                    style={{
                        padding: '4px 0px 4px 0px',
                        width: '100%',
                        border: 'none',
                        background: 'transparent',
                        cursor: 'pointer',
                    }}
                />
            )}
        </>
    );
};

const TableProduct = ({ initValue, page }: TableProductProps) => {
    const {
        control,
        watch,
        setValue,
        register,
        formState: { errors },
    } = useFormContext<IProduct>();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'productOptions',
    });
    useEffect(() => {
        if (
            initValue?.productOptions &&
            initValue.productOptions.length > 0 &&
            fields.length === 0
        ) {
            initValue.productOptions.forEach((option) => {
                append({
                    ...option,
                    yearsOfUse: option.yearsOfUse || 2025,
                    productOptionType: option.productOptionType || 1,
                });
            });
        }
    }, [initValue?.productOptions, append, fields.length]);

    useEffect(() => {
        fields.forEach((_, index) => {
            setValue(`productOptions.${index}.yearsOfUse`, 2025);
            setValue(`productOptions.${index}.productOptionType`, 1);
        });
    }, [fields, setValue]);

    const handleAddRow = () => {
        append({
            installationType: undefined,
            userCount: undefined,
            basePrice: undefined,
            yearsOfUse: 2025,
            productOptionType: 1,
        });
    };

    const handleRemoveRow = (index: number) => {
        remove(index);
    };

    const watchedValues = watch('productOptions');

    const getInstallationTypeLabel = (value: number) => {
        const option = installationType.find(
            (item) => item.value === value.toString(),
        );
        return option ? option.label : '';
    };

    // Hàm kiểm tra lỗi cho từng field
    const getFieldError = (fieldName: string, index: number) => {
        return errors.productOptions?.[index]?.[
            fieldName as keyof (typeof errors.productOptions)[0]
        ];
    };

    return (
        <Col xs={12}>
            <div>
                {page !== 'chi-tiet' && (
                    <div
                        style={{
                            marginTop: '16px',
                            display: 'flex',
                            justifyContent: 'flex-end',
                        }}
                    >
                        <Button
                            onClick={handleAddRow}
                            style={{
                                backgroundColor: '#ffffff',
                                borderColor: '#0ab39c',
                                color: '#0ab39c',
                            }}
                        >
                            + Thêm tùy chọn sản phẩm
                        </Button>
                    </div>
                )}

                <Table style={{ marginTop: '20px' }}>
                    <thead
                        style={{
                            backgroundColor: '#f3f6f9',
                        }}
                    >
                        <tr>
                            <th style={{ width: '5%' }}>STT</th>
                            <th style={{ width: '30%' }}>Kiểu cài đặt</th>
                            <th style={{ width: '30%' }}>Số lượng user</th>
                            <th style={{ width: '25%' }}>Giá gốc (USD)</th>
                            {page !== 'chi-tiet' && (
                                <th style={{ width: '10%' }}>Thao tác</th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {fields.length === 0 && (
                            <tr>
                                <td
                                    colSpan={5}
                                    style={{
                                        textAlign: 'center',
                                        padding: '20px',
                                        color: '#6c757d',
                                    }}
                                >
                                    Chưa có thông tin
                                </td>
                            </tr>
                        )}

                        {fields.map((field, index) => {
                            const currentValue = watchedValues?.[index];
                            const installationTypeValue =
                                currentValue?.installationType ||
                                field.installationType ||
                                1;
                            const userCountValue =
                                currentValue?.userCount || field.userCount;
                            const basePriceValue =
                                currentValue?.basePrice || field.basePrice;

                            // Kiểm tra lỗi cho từng field
                            const userCountError = getFieldError(
                                'userCount',
                                index,
                            );
                            const basePriceError = getFieldError(
                                'basePrice',
                                index,
                            );

                            return (
                                <>
                                    <tr key={field.id}>
                                        <td>{index + 1}</td>
                                        <td>
                                            {page === 'chi-tiet' ? (
                                                getInstallationTypeLabel(
                                                    installationTypeValue,
                                                )
                                            ) : (
                                                <select
                                                    {...register(
                                                        `productOptions.${index}.installationType`,
                                                    )}
                                                    style={{
                                                        padding:
                                                            '4px 0px 4px 0px',
                                                        width: '30%',
                                                        border: 'none',
                                                        background:
                                                            'transparent',
                                                    }}
                                                >
                                                    {installationType.map(
                                                        (option) => (
                                                            <option
                                                                key={
                                                                    option.value
                                                                }
                                                                value={
                                                                    option.value
                                                                }
                                                            >
                                                                {option.label}
                                                            </option>
                                                        ),
                                                    )}
                                                </select>
                                            )}
                                        </td>
                                        <td>
                                            {page === 'chi-tiet' ? (
                                                userCountValue
                                            ) : (
                                                <input
                                                    type='number'
                                                    {...register(
                                                        `productOptions.${index}.userCount`,
                                                        {
                                                            valueAsNumber: true,
                                                            required:
                                                                'Số lượng user không được để trống',
                                                            min: {
                                                                value: 1,
                                                                message:
                                                                    'Số lượng user phải lớn hơn 0',
                                                            },
                                                        },
                                                    )}
                                                    placeholder='Nhập số lượng user'
                                                    min='0'
                                                    style={{
                                                        padding:
                                                            '4px 0px 4px 0px',
                                                        width: '100%',
                                                        border: 'none',
                                                    }}
                                                />
                                            )}
                                        </td>
                                        <td>
                                            {page === 'chi-tiet' ? (
                                                basePriceValue &&
                                                !isNaN(basePriceValue) &&
                                                basePriceValue > 0 ? (
                                                    formatUSD(basePriceValue)
                                                ) : (
                                                    ''
                                                )
                                            ) : (
                                                <div>
                                                    <CurrencyInput
                                                        name={`productOptions.${index}.basePrice`}
                                                        placeholder='Nhập giá gốc'
                                                        value={basePriceValue}
                                                        setValue={setValue}
                                                    />
                                                    <input
                                                        {...register(
                                                            `productOptions.${index}.basePrice`,
                                                            {
                                                                valueAsNumber:
                                                                    true,
                                                                required:
                                                                    'Giá gốc không được để trống',
                                                                min: {
                                                                    value: 0.01,
                                                                    message:
                                                                        'Giá gốc phải lớn hơn 0',
                                                                },
                                                            },
                                                        )}
                                                        type='hidden'
                                                    />
                                                </div>
                                            )}
                                        </td>
                                        {page !== 'chi-tiet' && (
                                            <td>
                                                <button
                                                    type='button'
                                                    onClick={() =>
                                                        handleRemoveRow(index)
                                                    }
                                                    title='Xóa'
                                                    style={{
                                                        color: '#e74c3c',
                                                        fontSize: '20px',
                                                        cursor: 'pointer',
                                                    }}
                                                >
                                                    <i className='ri-delete-bin-line'></i>
                                                </button>
                                            </td>
                                        )}
                                    </tr>
                                    {/* Hàng hiển thị lỗi */}
                                    {(userCountError || basePriceError) && (
                                        <tr>
                                            <td></td>
                                            <td></td>
                                            <td>
                                                {userCountError && (
                                                    <div
                                                        style={{
                                                            color: '#e74c3c',
                                                            fontSize: '12px',
                                                            marginTop: '2px',
                                                        }}
                                                    >
                                                        {typeof userCountError ===
                                                        'string'
                                                            ? userCountError
                                                            : (
                                                                  userCountError as { message?: string }
                                                              )?.message}
                                                    </div>
                                                )}
                                            </td>
                                            <td>
                                                {basePriceError && (
                                                    <div
                                                        style={{
                                                            color: '#e74c3c',
                                                            fontSize: '12px',
                                                            marginTop: '2px',
                                                        }}
                                                    >
                                                        Giá gốc phải lớn hơn 0
                                                    </div>
                                                )}
                                            </td>
                                            {page !== 'chi-tiet' && <td></td>}
                                        </tr>
                                    )}
                                </>
                            );
                        })}
                    </tbody>
                </Table>
            </div>
        </Col>
    );
};
export default TableProduct;
