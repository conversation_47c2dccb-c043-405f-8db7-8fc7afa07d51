import { Option } from '@/types/app.type';
import {
    AddressType,
    CommonStatus,
    CompanyType,
    DeliveryType,
    DocumentIncludedType,
    InstallationType,
    LeadStatusEnum,
    LifecycleStageEnum,
    PaymentDocumentType,
    PaymentType,
    ProductOptionType,
    RegionDeliveryType,
} from './sharedData.enums';
import {
    IAddressType,
    ICompanyType,
    IProductOptionType,
} from './sharedData.type';

export const companiesType: ICompanyType[] = [
    {
        value: CompanyType.Parner,
        label: 'Đối tác',
    },
    {
        value: CompanyType.Vendor,
        label: 'Nhà cung cấp',
    },
    {
        value: CompanyType.Reseller,
        label: 'Người bán lại',
    },
    {
        value: CompanyType.Prospect,
        label: 'Khách hàng tiềm năng',
    },
    {
        value: CompanyType.Other,
        label: 'Khác',
    },
];

export const honorifics: Option[] = [
    {
        value: '0',
        label: 'Anh',
    },
    {
        value: '1',
        label: 'Chị',
    },
    {
        value: '2',
        label: 'Ông',
    },
    {
        value: '3',
        label: 'Bà',
    },
];
export const LeadStatus: Option[] = [
    {
        value: LeadStatusEnum.New.toString(),
        label: 'Mới',
    },
    {
        value: LeadStatusEnum.Open.toString(),
        label: 'Đang xử lý',
    },
    {
        value: LeadStatusEnum.Inprogress.toString(),
        label: 'Đang trao đổi',
    },
    {
        value: LeadStatusEnum.OpenDeal.toString(),
        label: 'Cơ hội bán hàng',
    },
    {
        value: LeadStatusEnum.Unqualified.toString(),
        label: 'Không phù hợp',
    },
    {
        value: LeadStatusEnum.AttemptedToContact.toString(),
        label: 'Liên hệ thất bại',
    },
    {
        value: LeadStatusEnum.Connected.toString(),
        label: 'Liên hệ được thiết lập',
    },
    {
        value: LeadStatusEnum.BadTiming.toString(),
        label: 'Chưa sẵn sàng',
    },
];

export const LifecycleStage: Option[] = [
    {
        value: LifecycleStageEnum.Subscriber.toString(),
        label: 'Người theo dõi',
    },
    {
        value: LifecycleStageEnum.Lead.toString(),
        label: 'Tiềm năng',
    },
    {
        value: LifecycleStageEnum.MarketingQualifiedLead.toString(),
        label: 'Tiềm năng marketing',
    },
    {
        value: LifecycleStageEnum.Opportunity.toString(),
        label: 'Cơ hội',
    },
    {
        value: LifecycleStageEnum.Customer.toString(),
        label: 'Khách hàng',
    },
    {
        value: LifecycleStageEnum.Evangelist.toString(),
        label: 'Khách hàng trung thành',
    },
    {
        value: LifecycleStageEnum.Other.toString(),
        label: 'Khác',
    },
];

export const addressTypes: IAddressType[] = [
    {
        value: AddressType.ContactAddress.toString(),
        label: 'Địa chỉ liên hệ',
    },
    {
        value: AddressType.BillingAddress.toString(),
        label: 'Địa chỉ hóa đơn',
    },
    {
        value: AddressType.ShippingAddress.toString(),
        label: 'Địa chỉ giao hàng',
    },
];

export const commonStatus: Option[] = [
    {
        value: CommonStatus.Active.toString(),
        label: 'Đang hoạt động',
    },
    {
        value: CommonStatus.InActive.toString(),
        label: 'Ngừng hoạt động',
    },
];

export const productOptionType: IProductOptionType[] = [
    {
        value: ProductOptionType.ConcurrentUsers.toString(),
        label: 'ConcurrentUsers',
    },
    {
        value: ProductOptionType.Server.toString(),
        label: 'Server',
    },
];
export const installationType: Option[] = [
    {
        value: InstallationType.Cloud.toString(),
        label: 'Cloud',
    },
    {
        value: InstallationType.OnPremises.toString(),
        label: 'On-premises',
    },
];
export const paymentType: Option[] = [
    {
        value: PaymentType.Domestic.toString(),
        label: 'Tạm ứng',
    },
    {
        value: PaymentType.Import.toString(),
        label: 'Thanh toán',
    },
];

export const paymentDocumentType: Option[] = [
    {
        value: PaymentDocumentType.AdvanceGuaranteeFromBank.toString(),
        label: 'Giấy bảo lãnh tạm ứng của ngân hàng',
    },
    {
        value: PaymentDocumentType.AdvanceRequestFromPartyB.toString(),
        label: 'Giấy đề nghị tạm ứng của bên B',
    },
];

export const deliveryType: Option[] = [
    {
        value: DeliveryType.BasedOnAdvanceDate.toString(),
        label: 'Theo ngày tạm ứng',
    },
    {
        value: DeliveryType.BasedOnContractSignDate.toString(),
        label: 'Theo ngày ký hợp đồng',
    },
];
export const regionDeliveryType: Option[] = [
    {
        value: RegionDeliveryType.MB.toString(),
        label: 'Miền Bắc',
    },
    {
        value: RegionDeliveryType.MN.toString(),
        label: 'Miền Nam',
    },
];

export const documentIncludedType: Option[] = [
    {
        value: DocumentIncludedType.GoodsHandoveRecord.toString(),
        label: 'Biên bản bàn giao hàng',
    },
    {
        value: DocumentIncludedType.VatInvoice.toString(),
        label: 'Hóa đơn GTGT',
    },
];
